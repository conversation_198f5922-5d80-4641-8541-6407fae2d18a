/**
 * 引擎服务Hook
 * 提供与DL引擎交互的功能
 */
import { useState, useEffect, useCallback, useRef } from 'react';
import { message } from 'antd';

// 引擎上下文接口
export interface EngineContext {
  scene: {
    entities: any[];
    cameras: any[];
    lights: any[];
    materials: any[];
  };
  performance: {
    fps: number;
    memory: number;
    drawCalls: number;
  };
  user: {
    id: string;
    permissions: string[];
  };
}

// 实体创建参数
export interface CreateEntityParams {
  type: string;
  name?: string;
  position?: [number, number, number];
  rotation?: [number, number, number];
  scale?: [number, number, number];
  properties?: Record<string, any>;
}

// 组件更新参数
export interface UpdateComponentParams {
  entityId: string;
  componentType: string;
  properties: Record<string, any>;
}

/**
 * 引擎服务Hook
 */
export function useEngineService() {
  // 状态
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [context, setContext] = useState<EngineContext | null>(null);
  
  // 引擎实例引用
  const engineRef = useRef<any>(null);

  // 初始化引擎连接
  useEffect(() => {
    const initializeEngine = async () => {
      try {
        setIsLoading(true);
        
        // 模拟引擎初始化
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 模拟引擎实例
        engineRef.current = {
          scene: {
            entities: [],
            cameras: [],
            lights: [],
            materials: []
          },
          performance: {
            fps: 60,
            memory: 512,
            drawCalls: 100
          }
        };
        
        setIsConnected(true);
        setContext({
          scene: engineRef.current.scene,
          performance: engineRef.current.performance,
          user: {
            id: 'current_user',
            permissions: ['read', 'write', 'admin']
          }
        });
        
      } catch (error) {
        console.error('引擎初始化失败:', error);
        message.error('引擎连接失败');
      } finally {
        setIsLoading(false);
      }
    };

    initializeEngine();
  }, []);

  /**
   * 获取当前上下文
   */
  const getCurrentContext = useCallback(async (): Promise<EngineContext | null> => {
    if (!isConnected || !engineRef.current) {
      return null;
    }

    // 模拟获取最新上下文
    const updatedContext: EngineContext = {
      scene: {
        entities: engineRef.current.scene.entities || [],
        cameras: engineRef.current.scene.cameras || [],
        lights: engineRef.current.scene.lights || [],
        materials: engineRef.current.scene.materials || []
      },
      performance: {
        fps: Math.floor(Math.random() * 60) + 30,
        memory: Math.floor(Math.random() * 1024) + 256,
        drawCalls: Math.floor(Math.random() * 200) + 50
      },
      user: {
        id: 'current_user',
        permissions: ['read', 'write', 'admin']
      }
    };

    setContext(updatedContext);
    return updatedContext;
  }, [isConnected]);

  /**
   * 创建实体
   */
  const createEntity = useCallback(async (params: CreateEntityParams): Promise<string> => {
    if (!isConnected || !engineRef.current) {
      throw new Error('引擎未连接');
    }

    try {
      setIsLoading(true);
      
      // 模拟创建实体
      const entityId = `entity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const entity = {
        id: entityId,
        type: params.type,
        name: params.name || `${params.type}_${entityId.substr(-6)}`,
        position: params.position || [0, 0, 0],
        rotation: params.rotation || [0, 0, 0],
        scale: params.scale || [1, 1, 1],
        properties: params.properties || {},
        createdAt: Date.now()
      };

      engineRef.current.scene.entities.push(entity);
      
      message.success(`已创建实体: ${entity.name}`);
      
      return entityId;
      
    } catch (error) {
      console.error('创建实体失败:', error);
      message.error('创建实体失败');
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected]);

  /**
   * 更新组件
   */
  const updateComponent = useCallback(async (params: UpdateComponentParams): Promise<void> => {
    if (!isConnected || !engineRef.current) {
      throw new Error('引擎未连接');
    }

    try {
      setIsLoading(true);
      
      // 模拟更新组件
      const entity = engineRef.current.scene.entities.find((e: any) => e.id === params.entityId);
      
      if (!entity) {
        throw new Error(`实体不存在: ${params.entityId}`);
      }

      // 更新组件属性
      if (!entity.components) {
        entity.components = {};
      }
      
      entity.components[params.componentType] = {
        ...entity.components[params.componentType],
        ...params.properties
      };
      
      entity.updatedAt = Date.now();
      
      message.success(`已更新组件: ${params.componentType}`);
      
    } catch (error) {
      console.error('更新组件失败:', error);
      message.error('更新组件失败');
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected]);

  /**
   * 删除实体
   */
  const deleteEntity = useCallback(async (entityId: string): Promise<void> => {
    if (!isConnected || !engineRef.current) {
      throw new Error('引擎未连接');
    }

    try {
      setIsLoading(true);
      
      const entityIndex = engineRef.current.scene.entities.findIndex((e: any) => e.id === entityId);
      
      if (entityIndex === -1) {
        throw new Error(`实体不存在: ${entityId}`);
      }

      const entity = engineRef.current.scene.entities[entityIndex];
      engineRef.current.scene.entities.splice(entityIndex, 1);
      
      message.success(`已删除实体: ${entity.name}`);
      
    } catch (error) {
      console.error('删除实体失败:', error);
      message.error('删除实体失败');
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected]);

  /**
   * 获取性能统计
   */
  const getPerformanceStats = useCallback(async () => {
    if (!isConnected || !engineRef.current) {
      return null;
    }

    return {
      fps: Math.floor(Math.random() * 60) + 30,
      memory: Math.floor(Math.random() * 1024) + 256,
      drawCalls: Math.floor(Math.random() * 200) + 50,
      triangles: Math.floor(Math.random() * 100000) + 10000,
      textures: Math.floor(Math.random() * 50) + 10
    };
  }, [isConnected]);

  return {
    // 状态
    isConnected,
    isLoading,
    context,
    
    // 方法
    getCurrentContext,
    createEntity,
    updateComponent,
    deleteEntity,
    getPerformanceStats,
    
    // 计算属性
    isReady: isConnected && !isLoading
  };
}
